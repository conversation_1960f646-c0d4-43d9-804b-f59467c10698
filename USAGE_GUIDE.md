# 智慧图片生成 MCP 使用指南

## 🎯 项目目标

zhihui-mcp 旨在解决网页开发中的图片生成问题：
- ✅ AI生成的网页代码中图片URL经常失效
- ✅ 手动搜索合适图片费时费力  
- ✅ 自己制作图片需要大量时间和设计技能

## 🔧 在不同AI编辑器中配置

### Cursor 配置

1. 打开 Cursor 设置
2. 找到 MCP 配置文件（通常在 `~/.cursor/mcp_servers.json`）
3. 添加以下配置：

```json
{
  "mcpServers": {
    "zhihui-image": {
      "command": "node",
      "args": ["D:\\path\\to\\zhihui-mcp\\build\\index.js"],
      "env": {
        "MODELSCOPE_API_KEY": "your-api-key-here",
        "MODELSCOPE_MODEL_ID": "damo/text_to_image_synthesis-stable_diffusion_xl-base-1.0"
      }
    }
  }
}
```

### Augment 配置

在 Augment 的设置中添加 MCP 服务器配置，格式类似。

### Claude Desktop 配置

在 `claude_desktop_config.json` 中添加：

```json
{
  "mcpServers": {
    "zhihui-image": {
      "command": "node", 
      "args": ["D:\\path\\to\\zhihui-mcp\\build\\index.js"],
      "env": {
        "MODELSCOPE_API_KEY": "your-api-key-here"
      }
    }
  }
}
```

## 🚀 实际使用场景

### 场景1：创建公司官网

**用户输入：**
```
帮我创建一个现代化科技公司的官网首页，公司名叫"智能未来"，主要做AI产品开发
```

**AI自动调用：**
1. `analyze_and_generate` - 生成公司logo
2. `analyze_and_generate` - 生成hero背景图
3. `analyze_and_generate` - 生成产品展示图片

**生成的HTML会直接包含：**
```html
<img src="./generated_images/logo_1640995200000_abc123.png" alt="智能未来Logo">
<div class="hero" style="background-image: url('./generated_images/hero_1640995200000_def456.png')">
```

### 场景2：博客网站

**用户输入：**
```
创建一个个人技术博客，需要头像、文章缩略图和背景
```

**AI自动处理：**
- 生成个人头像 (avatar, 128x128)
- 生成文章缩略图 (thumbnail, 300x200) 
- 生成页面背景 (section-background, 1200x600)

### 场景3：电商产品页

**用户输入：**
```
设计一个销售智能手表的产品页面
```

**AI会生成：**
- 产品展示图 (card-image, 400x300)
- 功能图标 (icon, 64x64)
- 横幅广告 (banner, 800x200)

## 📋 魔搭平台模型推荐

### 推荐的文生图模型

1. **Stable Diffusion XL**
   - 模型ID: `damo/text_to_image_synthesis-stable_diffusion_xl-base-1.0`
   - 特点：高质量，适合各种风格

2. **通义万相**
   - 模型ID: `damo/multi_modal_chat-qwen-vl-chat-1_1`
   - 特点：中文理解好，适合中文场景

3. **其他模型**
   - 访问 [魔搭模型库](https://www.modelscope.cn/models?page=1&tasks=text-to-image-synthesis) 查看更多

### 获取API密钥步骤

1. 访问 [魔搭平台](https://www.modelscope.cn/)
2. 注册并登录账户
3. 进入个人中心 → API管理
4. 创建新的API密钥
5. 复制密钥到配置文件中

## 🎨 高级使用技巧

### 1. 自定义提示词优化

在 `analyze_and_generate` 中使用 `context` 参数：

```json
{
  "webpageContent": "科技公司官网",
  "imageType": "logo", 
  "context": "简约风格，蓝色主色调，体现科技感和专业性"
}
```

### 2. 批量生成图片

虽然当前版本没有实现 `batch_generate_images`，但可以连续调用：

```javascript
// AI会自动进行多次调用
const images = [
  await callTool('analyze_and_generate', {webpageContent: '...', imageType: 'logo'}),
  await callTool('analyze_and_generate', {webpageContent: '...', imageType: 'hero'}),
  await callTool('analyze_and_generate', {webpageContent: '...', imageType: 'icon'})
];
```

### 3. 尺寸优化

使用 `smart_size_inference` 获取最佳尺寸建议：

```json
{
  "imageType": "hero-background",
  "usageContext": "移动端首页背景"
}
```

## 🔍 故障排除

### 问题1：API调用失败

**症状：** 返回"图片生成失败"错误

**解决方案：**
1. 检查API密钥是否正确
2. 确认网络连接正常
3. 验证模型ID是否存在
4. 检查API配额是否充足

### 问题2：图片保存失败

**症状：** 生成成功但无法保存到本地

**解决方案：**
1. 检查 `OUTPUT_DIR` 目录权限
2. 确保磁盘空间充足
3. 验证路径格式正确（Windows使用反斜杠）

### 问题3：MCP服务器无法启动

**症状：** AI编辑器显示连接失败

**解决方案：**
1. 确认已运行 `npm run build`
2. 检查Node.js版本 >= 18.0.0
3. 验证配置文件路径正确
4. 查看控制台错误日志

## 📈 性能优化建议

1. **合理设置图片尺寸**：避免生成过大的图片
2. **使用缓存**：相同参数的请求会复用结果
3. **批量处理**：一次性生成多张相关图片
4. **选择合适模型**：根据需求选择速度和质量平衡的模型

## 🔮 未来功能规划

- [ ] 支持更多图片格式 (WebP, SVG)
- [ ] 图片风格一致性保持
- [ ] 智能色彩搭配
- [ ] 图片编辑和后处理
- [ ] 批量生成优化
- [ ] 图片质量评估

## 💡 最佳实践

1. **描述要具体**：提供详细的网页内容描述
2. **指定图片类型**：明确说明需要什么类型的图片
3. **提供上下文**：说明图片的使用场景和风格要求
4. **测试不同模型**：尝试不同的模型找到最适合的
5. **保存配置**：记录好用的参数配置以便复用
