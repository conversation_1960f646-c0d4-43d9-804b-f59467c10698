# 智慧图片生成 MCP (zhihui-mcp)

一个基于魔搭平台API的智能图片生成MCP工具，专为网页开发中的图片生成问题而设计。

## 🌟 核心功能

- **自动触发机制**：AI生成网页代码时自动调用图片生成
- **智能图片生成**：根据网页内容、设计风格自动生成适配图片
- **规格适配**：自动推断合适的图片尺寸和格式
- **无缝集成**：生成的图片可直接在代码中引用
- **多种图片类型**：支持logo、图标、背景图、内容图片等

## 🚀 快速开始

### 1. 安装依赖

```bash
npm install
```

### 2. 编译项目

```bash
npm run build
```

### 3. 配置环境变量

在你的AI编辑器（如Cursor、Augment）的MCP配置中添加：

```json
{
  "mcpServers": {
    "zhihui-image": {
      "command": "node",
      "args": ["path/to/zhihui-mcp/build/index.js"],
      "env": {
        "MODELSCOPE_API_KEY": "your-modelscope-api-key",
        "MODELSCOPE_MODEL_ID": "damo/text_to_image_synthesis-stable_diffusion_xl-base-1.0",
        "MODELSCOPE_BASE_URL": "https://api.modelscope.cn",
        "OUTPUT_DIR": "./generated_images",
        "MAX_IMAGE_SIZE": "2048"
      }
    }
  }
}
```

### 4. 获取魔搭平台API密钥

1. 访问 [魔搭平台](https://www.modelscope.cn/)
2. 注册并登录账户
3. 在个人中心获取API密钥
4. 选择合适的文生图模型ID

## 🛠️ 支持的工具

### 1. generate_image

基础图片生成功能

**参数：**

- `prompt` (必需): 图片生成的提示词
- `width` (可选): 图片宽度，默认512
- `height` (可选): 图片高度，默认512
- `imageType` (可选): 图片类型标识

**示例：**

```json
{
  "prompt": "modern logo design, clean, professional",
  "width": 200,
  "height": 80,
  "imageType": "logo"
}
```

### 2. analyze_and_generate (核心功能)

智能分析网页内容并自动生成适配图片

**参数：**

- `webpageContent` (必需): 网页内容或描述
- `imageType` (必需): 图片类型 (logo/icon/background/hero/card/avatar/banner)
- `context` (可选): 额外的上下文信息
- `customSize` (可选): 自定义尺寸

**示例：**

```json
{
  "webpageContent": "一个现代化的科技公司官网，主要展示AI产品和服务",
  "imageType": "hero",
  "context": "需要体现科技感和专业性"
}
```

### 3. smart_size_inference

智能尺寸推断

**参数：**

- `imageType` (必需): 图片类型
- `usageContext` (可选): 使用场景描述

## 📏 支持的图片类型和预设尺寸

| 类型               | 尺寸      | 用途     |
| ------------------ | --------- | -------- |
| logo               | 200x80    | 网站标志 |
| icon               | 64x64     | 功能图标 |
| button-icon        | 32x32     | 按钮图标 |
| hero-background    | 1920x1080 | 首页背景 |
| section-background | 1200x600  | 区块背景 |
| card-image         | 400x300   | 卡片图片 |
| avatar             | 128x128   | 用户头像 |
| thumbnail          | 300x200   | 缩略图   |
| banner             | 800x200   | 横幅图片 |
| square             | 512x512   | 方形图片 |

## 🔧 环境变量配置

| 变量名              | 描述            | 默认值                                                    |
| ------------------- | --------------- | --------------------------------------------------------- |
| MODELSCOPE_API_KEY  | 魔搭平台API密钥 | 无（必需）                                                |
| MODELSCOPE_MODEL_ID | 使用的模型ID    | damo/text_to_image_synthesis-stable_diffusion_xl-base-1.0 |
| MODELSCOPE_BASE_URL | API基础URL      | https://api.modelscope.cn                                 |
| OUTPUT_DIR          | 图片保存目录    | ./generated_images                                        |
| MAX_IMAGE_SIZE      | 最大图片尺寸    | 2048                                                      |

## 📝 使用示例

### 在AI对话中使用

当你向AI描述网页需求时，AI会自动调用相应的工具：

```
用户：帮我创建一个科技公司的官网首页，需要包含logo和hero背景图

AI会自动：
1. 调用 analyze_and_generate 生成logo
2. 调用 analyze_and_generate 生成hero背景
3. 在生成的HTML代码中直接引用这些图片
```

### 返回结果格式

```json
{
  "success": true,
  "image": {
    "url": "./generated_images/hero_1640995200000_abc123.png",
    "localPath": "/absolute/path/to/image.png", 
    "width": 1920,
    "height": 1080,
    "format": "png",
    "type": "hero",
    "prompt": "modern tech background, futuristic, professional",
    "usageSuggestion": "适合用作网站首页背景图"
  }
}
```

## 🔍 故障排除

### 常见问题

1. **API密钥错误**

   - 检查 MODELSCOPE_API_KEY 是否正确设置
   - 确认API密钥有效且有足够配额
2. **图片生成失败**

   - 检查网络连接
   - 确认模型ID是否正确
   - 查看控制台错误日志
3. **文件保存失败**

   - 检查 OUTPUT_DIR 目录权限
   - 确保有足够的磁盘空间

## 📄 许可证

MIT License

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！
